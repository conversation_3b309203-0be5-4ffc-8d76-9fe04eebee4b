'use client'
import { useGetProductDetail } from '../../hooks/query/useGetProductDetail'
import ProductDetailCommon from '../components/product-detail/ProductDetailCommon'
import ProductDetailContent from '../components/product-detail/ProductDetailContent'
import ProductDetailStore from '../components/product-detail/ProductDetailStore'
import ProductDetailSkeleton from '../components/product-detail/ProductDetailSkeleton'

type ProductDetailWrapperProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
}
const ProductDetailWrapper: React.FC<ProductDetailWrapperProps> = ({ paramsValue }) => {
  const { id } = paramsValue

  const { productDetail, isGetProductDetailLoading } = useGetProductDetail({ id })

  const heroImage = productDetail?.heroImage
  const url = typeof heroImage === 'object' ? heroImage.url : null
  const thumbnailURL = typeof heroImage === 'object' ? heroImage.thumbnailURL : null

  if (isGetProductDetailLoading || !productDetail) {
    return <ProductDetailSkeleton />
  }

  return (
    <div className="h-full w-full max-w-[calc(100vw-320px)] bg-custom-background-hover px-16 py-6">
      <div className="flex w-full gap-6">
        <div className="h-fit flex-[3] shrink-0 rounded-xl bg-white p-4">
          <ProductDetailCommon thumbnailURL={url ?? thumbnailURL} product={productDetail} />
        </div>
        <div className="h-fit flex-[4.5] shrink-0 rounded-xl bg-white p-4">
          <ProductDetailContent product={productDetail} />
        </div>
        <div className="h-fit flex-[2.5] shrink-0 rounded-xl bg-white p-4">
          <ProductDetailStore product={productDetail} />
        </div>
      </div>
    </div>
  )
}

export default ProductDetailWrapper
