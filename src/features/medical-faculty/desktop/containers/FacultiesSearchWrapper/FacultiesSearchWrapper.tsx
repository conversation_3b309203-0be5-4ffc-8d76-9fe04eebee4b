'use client'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useDebounce } from '@/utilities/useDebounce'
import { parse } from 'qs-esm'
import { useEffect, useState } from 'react'
import FacultiesHeader from '../../components/FacultiesHeader/FacultiesHeader'
import FacultiesSearchList from '../../components/FacultiesSearchList/FacultiesSearchList'

type FacultiesSearchWrapperProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
}

const FacultiesSearchWrapper: React.FC<FacultiesSearchWrapperProps> = ({ paramsValue }) => {
  const { q: query } = parse(paramsValue as unknown as string)

  const [inputValue, setInputValue] = useState<string>('')

  const valueSearchDebounce = useDebounce(inputValue, 500)

  const { updateSearchQuery } = useSearchQuery()

  useEffect(() => {
    if (query !== undefined) {
      setInputValue(typeof query === 'string' ? query : query[0])
    }
  }, [query])

  useEffect(() => {
    updateSearchQuery({ q: valueSearchDebounce }, 'replace')
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [valueSearchDebounce])

  const handleSearchChange = (query: string) => {
    setInputValue(query)
  }

  return (
    <div className="h-full w-full max-w-[calc(100vw-320px)] bg-custom-background-hover px-16 py-6">
      <div className="w-full rounded-xl bg-white p-4">
        <FacultiesHeader
          onSearchChange={handleSearchChange}
          valueDefault={inputValue}
          isShowContactBox={false}
        />

        <FacultiesSearchList query={query} />
      </div>
    </div>
  )
}

export default FacultiesSearchWrapper
