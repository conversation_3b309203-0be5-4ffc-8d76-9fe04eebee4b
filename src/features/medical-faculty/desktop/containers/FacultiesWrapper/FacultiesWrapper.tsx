'use client'
import { useDebounce } from '@/utilities/useDebounce'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import FacultiesHeader from '../../components/FacultiesHeader/FacultiesHeader'
import FacultiesList from '../../components/FacultiesList/FacultiesList'

const FacultiesWrapper: React.FC = () => {
  const router = useRouter()

  const [searchValue, setSearchValue] = useState<string>('')

  const searchValueDebounce = useDebounce(searchValue, 500)

  useEffect(() => {
    if (searchValueDebounce)
      router.push(`/medical-handbook/faculties/search-faculties?q=${searchValueDebounce}`)
  }, [searchValueDebounce, router])

  const handleSearchChange = (query: string) => {
    setSearchValue(query)
  }

  return (
    <div className="w-full rounded-xl bg-white p-4">
      <FacultiesHeader onSearchChange={handleSearchChange} />

      <FacultiesList />
    </div>
  )
}

export default FacultiesWrapper
