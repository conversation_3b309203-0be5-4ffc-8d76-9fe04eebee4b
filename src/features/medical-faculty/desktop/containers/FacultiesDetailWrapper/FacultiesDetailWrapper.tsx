'use client'
import { useGetFaculty } from '@/hooks/query/faculty/useGetFaculty'
import { LocalizeField } from '@/types/custom-payload.type'
import { useParams } from 'next/navigation'
import { parse } from 'qs-esm'
import { useEffect, useState } from 'react'
import FacultyCategory, {
  ETypeCategory,
} from '../../components/FacultiesDetail/FacultyCategory/FacultyCategory'
import FacultyList from '../../components/FacultiesDetail/FacultyList/FacultyList'
import FacultySelectSymbol from '../../components/FacultiesDetail/FacultySelectSymbol/FacultySelectSymbol'
import FacultyStep from '../../components/FacultiesDetail/FacultyStep/FacultyStep'
import FacultyWarning from '../../components/FacultiesSearchList/FacultyWarning/FacultyWarning'

type FacultyDetailWrapperProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
}
const FacultiesDetailWrapper: React.FC<FacultyDetailWrapperProps> = ({ paramsValue }) => {
  const [selectedType, setSelectedType] = useState<ETypeCategory>(ETypeCategory.PATIENT)

  const { q: query } = parse(paramsValue as unknown as string)

  const [activeStepPatient, setActiveStepPatient] = useState<string | null>(null)
  const [activeStepDoctor, setActiveStepDoctor] = useState<string | null>(null)

  const params = useParams()

  const { id: idFaculty } = params

  const { faculty, isGetFacultyLoading } = useGetFaculty({
    id: (idFaculty as string) ?? '',
    params: {
      locale: 'all',
    },
    useQueryOptions: {
      enabled: !!idFaculty,
    },
  })

  const name = faculty?.name as unknown as LocalizeField<string>

  const questions = faculty?.questions

  const questionsPatient = questions?.filter((question) => {
    const category = (typeof question.question !== 'string' && question.question.category) as string
    return category === ETypeCategory.PATIENT
  })

  const questionsDoctor = questions?.filter((question) => {
    const category = (typeof question.question !== 'string' && question.question.category) as string
    return category === ETypeCategory.DOCTOR
  })
  useEffect(() => {
    if (questionsPatient && questionsPatient.length > 0) {
      setActiveStepPatient(questionsPatient[0].id ?? null)
    }
  }, [questionsPatient])

  useEffect(() => {
    if (questionsDoctor && questionsDoctor.length > 0) {
      setActiveStepDoctor(questionsDoctor[0].id ?? null)
    }
  }, [questionsDoctor])

  const handleStepChange = (id: string) => {
    if (selectedType === ETypeCategory.PATIENT) {
      setActiveStepPatient(id)
    } else {
      setActiveStepDoctor(id)
    }
  }

  return (
    <div className="h-full max-h-[calc(100vh-90px)] w-full max-w-[calc(100vw-300px)] overflow-hidden bg-custom-background-hover px-16 py-6">
      <div className="flex h-full gap-6 overflow-hidden">
        <div className="flex h-full flex-[2] shrink-0 flex-col">
          <div className="rounded-xl bg-white p-4">
            <FacultyCategory
              isLoading={isGetFacultyLoading}
              name={name}
              selectedType={selectedType}
              onChangeType={setSelectedType}
            />
          </div>
          <div className="mt-6 flex h-full w-full overflow-hidden rounded-xl bg-white p-4">
            <FacultyList query={query} />
          </div>
        </div>
        <div className="h-full flex-[4] shrink-0 rounded-xl bg-white p-4">
          <FacultyWarning />

          <FacultySelectSymbol isLoading={isGetFacultyLoading} />
        </div>
        <div className="h-fit flex-[1] shrink-0 rounded-xl bg-white p-4">
          <FacultyStep
            activeStep={
              selectedType === ETypeCategory.DOCTOR ? activeStepDoctor : activeStepPatient
            }
            onChangeStep={handleStepChange}
            isLoading={isGetFacultyLoading}
            questions={selectedType === ETypeCategory.DOCTOR ? questionsDoctor : questionsPatient}
          />
        </div>
      </div>
    </div>
  )
}

export default FacultiesDetailWrapper
