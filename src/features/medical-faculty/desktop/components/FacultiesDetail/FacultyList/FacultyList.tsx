'use client'

import { HealthcareIcon } from '@/components/Icons/HealthcareIcon'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetInfiniteFaculties } from '@/hooks/query/faculty/useGetInfiniteFaculties'
import { Media } from '@/payload-types'
import { LocalizeField } from '@/types/custom-payload.type'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import ArrowIcon from '@/assets/icons/arrow-right.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { ParsedQs } from 'qs-esm'

type FacultyListProps = {
  query?: string | ParsedQs | string[] | ParsedQs[] | undefined
}
const FacultyList: React.FC<FacultyListProps> = ({ query }) => {
  const t = useTranslations()

  const param = useParams()

  const { id: idFaculty } = param

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const { faculties, isGetFacultiesLoading } = useGetInfiniteFaculties({
    params: {
      limit: 10,
      locale: 'all',
      select: {
        questions: false,
        createdAt: false,
        updatedAt: false,
      },
      where: {
        id: {
          not: idFaculty,
        },
      },
      // where: {
      //   'subscription.type': {
      //     equals: type,
      //   },
      // },
    },
    config: {
      enabled: !!idFaculty,
    },
  })

  const facultiesList = faculties?.pages.flatMap((page) => page?.docs)
  return (
    <div className="flex w-full flex-col gap-3">
      <div className="typo-body-3">{t('MES-130')}</div>

      <div className="flex flex-1 flex-col gap-2 overflow-auto">
        {isGetFacultiesLoading
          ? Array.from({ length: 5 }).map((_, index) => (
              <Skeleton key={index} className="h-[52px] w-full"></Skeleton>
            ))
          : facultiesList?.map((faculty) => {
              const icon = faculty.icon as Media
              const iconUrl = icon?.url

              const localizedName = faculty.name as unknown as LocalizeField<string>

              return (
                <Link
                  href={`/medical-handbook/faculties/${faculty.id}${query ? `?q=${query}` : ''}`}
                  key={faculty.id}
                  className="flex items-center justify-between gap-3 rounded-lg bg-custom-background-hover px-4 py-2"
                >
                  <div className="flex items-center gap-3">
                    {iconUrl ? (
                      <div className="relative h-[32px] w-[32px]">
                        <Image
                          src={iconUrl}
                          alt={'icon'}
                          fill
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <HealthcareIcon width={32} height={32} />
                    )}

                    <div className={`flex flex-col items-start justify-start gap-2`}>
                      <div className="typo-body-6 truncate">{localizedName?.[primaryLanguage]}</div>
                      <div className={`typo-body-7 truncate text-subdued`}>
                        {localizedName?.[secondaryLanguage]}
                      </div>
                    </div>
                  </div>

                  <Image alt="arrow" src={ArrowIcon} width={20} height={20} />
                </Link>
              )
            })}
      </div>
    </div>
  )
}

export default FacultyList
