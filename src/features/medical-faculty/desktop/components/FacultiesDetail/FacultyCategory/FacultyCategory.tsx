'use client'
import { But<PERSON> } from '@/components/ui/Button/Button'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import ArrowIcon from '@/assets/icons/arrow-down -gray.svg'
import ProgressIcon from '@/assets/icons/progress-status.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useCustomTranslation } from '@/contexts/TranslationContext/TranslationContext'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { LocalizeField } from '@/types/custom-payload.type'

export enum ETypeCategory {
  PATIENT = 'patient',
  DOCTOR = 'doctor',
}

type FacultyCategoryProps = {
  selectedType: ETypeCategory
  onChangeType: (type: ETypeCategory) => void
  isLoading?: boolean
  name: LocalizeField<string>
}
const FacultyCategory: React.FC<FacultyCategoryProps> = ({
  selectedType = 'patient',
  isLoading,
  name,
  onChangeType,
}) => {
  const t = useTranslations()

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const { t: tMulti } = useCustomTranslation()

  return (
    <div className="flex w-full flex-col gap-6">
      <Button variant={'blank'} className="flex items-center justify-start gap-3 p-0">
        <Image className="rotate-90" src={ArrowIcon} alt="arrow" width={20} height={20} />
        <div className="typo-button-3 text-subdued">{t('MES-77')}</div>
      </Button>

      {isLoading ? (
        <div className="flex flex-col gap-2">
          <Skeleton className="h-7 w-full"></Skeleton>
          <Skeleton className="h-5 w-3/4"></Skeleton>
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          <div className="typo-heading-7">{name[primaryLanguage]}</div>
          <div className="typo-body-4 text-subdued">{name[secondaryLanguage]}</div>
        </div>
      )}

      {/* divide */}
      {!isLoading && <div className="h-[1px] w-full bg-neutral-300"></div>}

      {/* box category */}
      {!isLoading && (
        <div className="flex flex-col gap-3">
          <div
            onClick={() => onChangeType(ETypeCategory.PATIENT)}
            className={`flex cursor-pointer items-center justify-between gap-2 rounded-xl border ${selectedType === ETypeCategory.PATIENT ? 'border-primary bg-primary-50 text-primary-500' : 'border-transparent text-subdued'} px-4 py-3`}
          >
            <div className="flex flex-col gap-2">
              <div className="typo-body-6">{tMulti('MES-123', primaryLanguage)}</div>
              <div className="typo-body-7">{tMulti('MES-123', secondaryLanguage)}</div>
            </div>

            <Image alt="check" src={ProgressIcon} width={20} height={20} />
          </div>

          <div
            onClick={() => onChangeType(ETypeCategory.DOCTOR)}
            className={`flex cursor-pointer items-center justify-between gap-2 rounded-xl border ${selectedType === ETypeCategory.DOCTOR ? 'border-primary bg-primary-50 text-primary-500' : 'border-transparent text-subdued'} px-4 py-3`}
          >
            <div className="flex flex-col gap-2">
              <div className="typo-body-6">{tMulti('MES-124', primaryLanguage)}</div>
              <div className="typo-body-7">{tMulti('MES-124', secondaryLanguage)}</div>
            </div>

            <Image alt="check" src={ProgressIcon} width={20} height={20} />
          </div>
        </div>
      )}
    </div>
  )
}

export default FacultyCategory
