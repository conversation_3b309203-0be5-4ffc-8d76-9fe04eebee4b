'use client'

import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { Faculty, Question } from '@/payload-types'
import { LocalizeField } from '@/types/custom-payload.type'
import React from 'react'

export type QuestionType = {
  step: string
  question: string | Question
  id?: string | null
}

type FacultyStepProps = {
  isLoading: boolean
  activeStep: string | null
  questions: Faculty['questions']
  onChangeStep: (id: string) => void
}
const FacultyStep: React.FC<FacultyStepProps> = ({
  isLoading,
  activeStep,
  questions,
  onChangeStep,
}) => {
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  return (
    <div className="flex flex-col items-center justify-center">
      {questions?.map((question, index) => {
        const name = question.step as unknown as LocalizeField<string>
        return (
          <div
            className="flex flex-col items-center justify-center"
            key={question.id}
            onClick={() => onChangeStep(question.id ?? '')}
          >
            {index !== 0 && (
              <div
                className={`my-1 min-h-[30px] w-[2px] flex-1 shrink-0 ${activeStep === question.id ? 'bg-primary-500' : 'bg-neutral-200'}`}
              ></div>
            )}
            <div
              className={`flex cursor-pointer flex-col items-center justify-center gap-2 text-center ${activeStep === question.id ? 'text-primary-500' : 'text-black'}`}
            >
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full border ${activeStep === question.id ? 'border-primary-500 text-primary-500' : 'border-black'}`}
              >
                {index + 1}
              </div>

              {/* <Image src={CircleTick} alt="tick" width={32} height={32} /> */}

              <div className="typo-body-6">{name[primaryLanguage]}</div>

              <div className="typo-body-7">{name[secondaryLanguage]}</div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default FacultyStep
