import { Skeleton } from '@/components/ui/Skeleton/Skeleton'

const FacultySymbolSkeleton: React.FC = () => {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between gap-3">
        <div>
          <Skeleton className="h-7 w-60" />
          <Skeleton className="mt-3 h-5 w-40" />
        </div>

        <Skeleton className="h-5 w-24" />
      </div>

      <Skeleton className="h-11 w-full" />

      <div className="grid grid-cols-2 gap-3">
        <div className="col-span-2 flex min-h-[90px] flex-col items-center justify-center gap-3 rounded-lg bg-neutral-50 px-3 py-4">
          <Skeleton className="h-6 w-[60px]" />
          <Skeleton className="h-6 w-[70%]" />
          <Skeleton className="h-6 w-full" />
        </div>

        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="flex min-h-[90px] flex-col items-center justify-center gap-3 rounded-lg bg-neutral-50 px-3 py-4"
          >
            <Skeleton className="h-6 w-[60px]" />
            <Skeleton className="h-6 w-[70%]" />
            <Skeleton className="h-6 w-full" />
          </div>
        ))}
      </div>
    </div>
  )
}

export default FacultySymbolSkeleton
