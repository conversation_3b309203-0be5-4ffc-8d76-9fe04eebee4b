'use client'
import { Input } from '@/components/ui/Input/Input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/Switch/switch'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import SearchIcon from '@/assets/icons/search-normal.svg'
import { Button } from '@/components/ui/Button/Button'
import FacultySymbol from '../FacultySymbol/FacultySymbol'

const FacultySymbolView: React.FC = () => {
  const t = useTranslations()
  return (
    <div className="mt-3 flex h-[calc(100vh-250px)] w-full flex-col gap-6">
      <div className="flex items-center justify-between gap-3">
        <div className="flex flex-col gap-2">
          <div className="typo-heading-7">Hôm nay bạn bị làm sao?</div>
          <div className="typo-body-4 text-subdued">今⽇はどうされましたか？</div>
        </div>

        <div className="flex cursor-pointer items-center space-x-2">
          <Switch id="toggleSubLanguage" />
          <Label htmlFor="toggleSubLanguage">{t('MES-134')}</Label>
        </div>
      </div>

      <div className="relative">
        <Input placeholder={t('MES-66')} className="w-full rounded-lg px-3 py-3 pl-[36px]" />

        <div className="absolute left-3 top-1/2 -translate-y-1/2">
          <Image src={SearchIcon} alt="search" width={20} height={20} />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <FacultySymbol />
      </div>

      <div className="flex items-center gap-3">
        <Button
          variant={'outline'}
          className="typo-button-3 flex flex-1 items-center justify-center rounded-lg p-3"
        >
          {t('MES-795')}
        </Button>

        <Button
          variant={'default'}
          className="typo-button-3 flex flex-1 items-center justify-center rounded-lg p-3"
        >
          {t('MES-122')}
        </Button>
      </div>
    </div>
  )
}

export default FacultySymbolView
