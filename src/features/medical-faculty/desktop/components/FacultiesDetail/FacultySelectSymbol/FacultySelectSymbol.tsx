import FacultySymbolSkeleton from './FacultySymbolSkeleton/FacultySymbolSkeleton'
import FacultySymbolView from './FacultySymbolView/FacultySymbolView'

type FacultySelectSymbolProps = {
  isLoading: boolean
}
const FacultySelectSymbol: React.FC<FacultySelectSymbolProps> = ({ isLoading }) => {
  return (
    <div className="mt-6 h-full">
      {isLoading ? <FacultySymbolSkeleton /> : <FacultySymbolView />}
    </div>
  )
}

export default FacultySelectSymbol
