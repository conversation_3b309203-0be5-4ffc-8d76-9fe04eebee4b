'use client'
import { But<PERSON> } from '@/components/ui/Button/Button'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import CallIcon from '@/assets/icons/call-fill-white.svg'
import CallPrimaryIcon from '@/assets/icons/call-primary.svg'
import CopyIcon from '@/assets/icons/copy.svg'
import CloseIcon from '@/assets/icons/exit.svg'
import { useDialog } from '@/hooks/common/useDialog'

type TranslatorContactPopUpProps = {
  close: () => void
}
const TranslatorContactPopUp: React.FC<TranslatorContactPopUpProps> = ({ close }) => {
  const t = useTranslations()

  return (
    <div className="flex h-full w-full flex-col gap-4 bg-white p-6">
      <div className="flex items-center justify-between gap-3">
        <div className="typo-heading-7 text-primary-500">{t('MES-794')}</div>

        <Button variant={'blank'} className="p-0" onClick={close}>
          <Image src={CloseIcon} alt="close" width={32} height={32} />
        </Button>
      </div>

      <div className="flex flex-1 flex-col gap-3 overflow-auto">
        <div className="flex items-center justify-between gap-3 rounded-lg bg-custom-background-hover px-4 py-2">
          <div className="flex items-center gap-3">
            <Image src={CallPrimaryIcon} alt="phone" width={20} height={20} />

            <div className="">
              <div className="typo-body-6">Phiên dịch viên Lê Thu hà</div>
              <div className="typo-body-7 text-primary-500">+84 0123 456 789</div>
            </div>
          </div>

          <Button variant={'blank'} className="p-0">
            <Image src={CopyIcon} alt="phone" width={20} height={20} />
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-end">
        <Button onClick={close} variant={'default'} className="px-6 py-3">
          {t('MES-58')}
        </Button>
      </div>
    </div>
  )
}

const TranslatorContact: React.FC = () => {
  const t = useTranslations()

  const { openDialog } = useDialog()

  const handleOpenModal = () => {
    openDialog({
      children: ({ close }) => <TranslatorContactPopUp close={close} />,
      variant: 'blank',
      wrapperClassName:
        'mobile-wrapper z-[1000] !min-w-[510px] !w-[510px] min-h-[400px] overflow-y-auto sm:min-w-0 rounded-xl',
    })
  }

  return (
    <div
      onClick={handleOpenModal}
      className="typo-body-6 flex w-full cursor-pointer items-center gap-2 whitespace-nowrap rounded-[99px] bg-[linear-gradient(305deg,#5BAEF3_-1.86%,#1764E0_75.91%),linear-gradient(306deg,#17D1B2_-0.7%,#02C2E9_81.23%)] px-4 py-2 text-white"
    >
      <Image src={CallIcon} alt="phone" width={20} height={20} />
      {t('MES-794')}
    </div>
  )
}

export default TranslatorContact
