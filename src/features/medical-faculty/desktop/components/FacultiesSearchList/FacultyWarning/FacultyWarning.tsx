'use client'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import warningIcon from '@/assets/icons/danger_triangle.svg'

const FacultyWarning: React.FC = () => {
  const t = useTranslations()
  return (
    <div className="mt-3 rounded-lg bg-red-50 p-3">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="warning" className="border-0">
          <AccordionTrigger className="p-0 pb-[6px] hover:no-underline">
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-3">
                <div className="size-5 shrink-0">
                  <Image src={warningIcon} alt="warning" width={20} height={20} />
                </div>
                <div className="typo-body-6 text-red-500">{t('MES-604')}</div>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-0 pl-8">
            <div className="typo-body-9">{t('MES-605')}</div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )
}

export default FacultyWarning
