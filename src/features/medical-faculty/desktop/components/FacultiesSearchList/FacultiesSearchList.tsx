'use client'
import { Accordion } from '@/components/ui/Accordion/Accordion'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import SearchEmpty from '@/features/home/<USER>/Search/SearchEmpty/SearchEmpty'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { useGetInfiniteFaculties } from '@/hooks/query/faculty/useGetInfiniteFaculties'
import { useTranslations } from 'next-intl'
import { ParsedQs } from 'qs-esm'
import { useEffect, useRef } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import FacultiesBox from '../FacultiesBox/FacultiesBox'
import FacultyWarning from './FacultyWarning/FacultyWarning'

type FacultiesSearchListType = {
  query: string | ParsedQs | string[] | ParsedQs[] | undefined
}

const SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'symptoms.name', type: 'like', isLocalized: true },
  { path: 'name', type: 'like', isLocalized: true },
  { path: 'bodyParts.name', type: 'like', isLocalized: true },
  { path: 'questions.question.keywords.name', type: 'like', isLocalized: true },
]

const FacultiesSearchList: React.FC<FacultiesSearchListType> = ({ query }) => {
  const t = useTranslations()

  const currentQuery = useRef<typeof query>(undefined)

  const { queryFilters } = useGenerateSearchQueryFilter({
    query: query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  useEffect(() => {
    currentQuery.current = query
  }, [query])

  const { faculties, fetchNextPage, hasNextPage, isGetFacultiesLoading } = useGetInfiniteFaculties({
    params: {
      limit: 40,
      locale: 'all',
      select: {
        questions: false,
        createdAt: false,
        updatedAt: false,
      },
      where: {
        and: [
          {
            or: queryFilters,
          },
        ],
      },
    },
  })

  const facultiesList = faculties?.pages.flatMap((page) => page?.docs)

  return (
    <>
      <FacultyWarning />

      <div className="mt-3">
        <div className="typo-body-3">
          {t('MES-71')} ({faculties?.pages[0]?.totalDocs ?? 0})
        </div>

        <div className="mt-3">
          {(isGetFacultiesLoading && !faculties) || currentQuery.current !== query ? (
            <div className="grid grid-cols-3 gap-3">
              {new Array(12).fill(0).map((_, i) => {
                return <Skeleton key={i} className="h-[52px] w-full"></Skeleton>
              })}
            </div>
          ) : (
            <>
              {faculties && (
                <InfiniteScroll
                  dataLength={faculties.pages.length}
                  next={() => {
                    if (hasNextPage) {
                      fetchNextPage()
                    }
                  }}
                  hasMore={hasNextPage}
                  loader={
                    <div className="flex items-center justify-center">
                      <Spinner></Spinner>
                    </div>
                  }
                  scrollThreshold={0.8}
                  className="space-y-3 !overflow-hidden"
                >
                  <Accordion
                    type="multiple"
                    className="grid grid-cols-3 gap-3"
                    key={'faculties'}
                    value={facultiesList?.map((faculty) => faculty.id)}
                  >
                    {facultiesList?.map((faculty) => {
                      return (
                        <FacultiesBox
                          faculty={faculty}
                          key={faculty.id}
                          isExpanded={true}
                          isPreventExpand={true}
                          isShowButtonExpand={false}
                          textColorExpand="!text-primary-500"
                          borderColorExpand="border-transparent"
                          backgroundColorExpand="bg-neutral-50"
                          directionName="flex-row"
                          query={currentQuery.current as string}
                        />
                      )
                    })}
                  </Accordion>
                </InfiniteScroll>
              )}
            </>
          )}

          {!isGetFacultiesLoading && faculties?.pages[0]?.totalDocs === 0 && <SearchEmpty />}
        </div>
      </div>
    </>
  )
}

export default FacultiesSearchList
