'use client'

import { Input } from '@/components/ui/Input/Input'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useEffect, useState } from 'react'

import CircleClose from '@/assets/icons/close-circle-gray-filled.svg'
import SearchIcon from '@/assets/icons/search-normal.svg'
import TranslatorContact from '../TranslatorContact/TranslatorContact'

type FacultiesHeaderProps = {
  onSearchChange?: (query: string) => void
  isShowContactBox?: boolean
  valueDefault?: string
}

const FacultiesHeader: React.FC<FacultiesHeaderProps> = ({
  onSearchChange,
  isShowContactBox = true,
  valueDefault = '',
}) => {
  const t = useTranslations()

  const [inputValue, setInputValue] = useState<string>('')

  useEffect(() => {
    onSearchChange?.(valueDefault)
    setInputValue(valueDefault)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [valueDefault])

  const handleClearSearch = () => {
    setInputValue('')
    onSearchChange?.('')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
    onSearchChange?.(e.target.value)
  }

  return (
    <div className="flex items-center justify-between gap-3">
      <div className="typo-heading-7 text-primary-500">{t('MES-33')}</div>

      <div className="flex items-center gap-3">
        <div className="relative w-full min-w-[350px]">
          <Input
            placeholder={t('MES-66')}
            className="w-full rounded-xl px-4 py-2 pr-6"
            value={inputValue ?? ''}
            onChange={handleInputChange}
          />
          <Image
            alt={inputValue ? 'clear search' : 'search'}
            src={inputValue ? CircleClose : SearchIcon}
            width={20}
            height={20}
            className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
            onClick={inputValue ? handleClearSearch : undefined}
          />
        </div>

        {isShowContactBox && <TranslatorContact />}
      </div>
    </div>
  )
}

export default FacultiesHeader
