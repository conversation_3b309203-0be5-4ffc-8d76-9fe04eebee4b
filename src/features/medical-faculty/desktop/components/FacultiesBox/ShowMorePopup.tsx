import {
  <PERSON>over,
  <PERSON>overArrow,
  <PERSON><PERSON>Content,
  PopoverTrigger,
} from '@/components/ui/Popover/Popover'
import { BodyPart, Symptom } from '@/payload-types'
import { LocalizeField } from '@/types/custom-payload.type'

type ShowMorePopupProps = {
  bodyParts: BodyPart[]
  symptoms: Symptom[]
  primaryLanguage: string
  secondaryLanguage: string
}
const ShowMorePopup: React.FC<ShowMorePopupProps> = ({
  bodyParts,
  symptoms,
  primaryLanguage,
  secondaryLanguage,
}) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="typo-body-6 inline-flex w-fit cursor-pointer rounded-md border border-primary bg-primary-50 px-2 py-1 text-primary-500">
          + {bodyParts.length + symptoms.length}
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="center"
        sideOffset={4}
        className="typo-body-7 relative rounded-[6px] border-none bg-custom-informative-600 p-3 text-white"
      >
        <div className="flex flex-col gap-3">
          {bodyParts?.map((part, index) => {
            const { name, id } = part as BodyPart
            const localizedBodyPartName = name as unknown as LocalizeField<string>
            return (
              localizedBodyPartName && (
                <div className="typo-body-6" key={`${id}-${index}`}>
                  {localizedBodyPartName?.[primaryLanguage]}{' '}
                  {secondaryLanguage && localizedBodyPartName?.[secondaryLanguage] && (
                    <span className="text-ellipsis">{` (${localizedBodyPartName?.[secondaryLanguage]})`}</span>
                  )}
                </div>
              )
            )
          })}

          <ul className="!list-disc space-y-2 px-5">
            {symptoms?.map((symptom, index) => {
              const { name, id } = symptom as Symptom
              const localizedSymptomName = name as unknown as LocalizeField<string>
              return (
                <li className="typo-body-7" key={`${id}-${index}`}>
                  {localizedSymptomName?.[primaryLanguage]}{' '}
                  {secondaryLanguage && localizedSymptomName?.[secondaryLanguage] && (
                    <span className="text-ellipsis">{` (${localizedSymptomName?.[secondaryLanguage]})`}</span>
                  )}
                </li>
              )
            })}
          </ul>
        </div>
        <PopoverArrow asChild>
          <svg
            className="rotate-180"
            width={16}
            height={8}
            viewBox="0 0 16 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M6.58579 1.41421C7.36684 0.633163 8.63316 0.633165 9.41421 1.41421L16 8L-3.49691e-07 8L6.58579 1.41421Z"
              fill="#3B82F6"
            />
          </svg>
        </PopoverArrow>
      </PopoverContent>
    </Popover>
  )
}

export default ShowMorePopup
