'use client'
import { Button } from '@/components/ui/Button/Button'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { BodyPart, Faculty, Media, Symptom } from '@/payload-types'
import { LocalizeField } from '@/types/custom-payload.type'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import arrowDown from '@/assets/icons/arrow-down -gray.svg'
import arrowPrimaryIcon from '@/assets/icons/arrow-down.svg'
import mapIcon from '@/assets/icons/map.svg'
import { HealthcareIcon } from '@/components/Icons/HealthcareIcon'
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import Link from 'next/link'
import { stringify } from 'qs-esm'
import ShowMorePopup from './ShowMorePopup'

type FacultiesBoxProp = {
  isShowButtonExpand?: boolean
  isExpanded?: boolean
  faculty: Faculty
  isPreventExpand?: boolean
  textColorExpand?: string
  borderColorExpand?: string
  backgroundColorExpand?: string
  directionName?: 'flex-col' | 'flex-row'
  query?: string
  onExpand?: (isExpanded: boolean) => void
}
const FacultiesBox: React.FC<FacultiesBoxProp> = ({
  isExpanded = false,
  isShowButtonExpand = true,
  isPreventExpand = false,
  textColorExpand = '!text-primary-500',
  borderColorExpand = 'border-primary',
  backgroundColorExpand = 'bg-primary-50',
  directionName = 'flex-col',
  faculty,
  query,
  onExpand,
}) => {
  const t = useTranslations()
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const localizedName = faculty.name as unknown as LocalizeField<string>

  const bodyParts = faculty.bodyParts?.filter(
    (body: BodyPart) => body.name,
  ) as unknown as BodyPart[]

  const bodyPartsSlice = bodyParts.slice(0, 2)

  const bodyPartsRemaining = bodyParts.slice(2)

  const symptoms = faculty.symptoms as unknown as Symptom[]

  const symptomsSlice = symptoms.slice(0, 2)

  const symptomsRemaining = symptoms.slice(2)

  const icon = faculty.icon as Media

  const handleExpandBox = (isExpanded: boolean) => {
    if (onExpand) {
      onExpand(isExpanded)
    }
  }

  const generateGoogleMapQuery = (name: string) => {
    return stringify({ query: name })
  }

  const googleMapUrl = `https://www.google.com/maps/search/?api=1&${generateGoogleMapQuery(localizedName?.['ja'])}`

  return (
    <AccordionItem
      value={faculty.id}
      className={`flex h-fit max-h-[350px] flex-col rounded-lg border p-4 ${isExpanded ? `min-h-[320px] ${borderColorExpand} ${backgroundColorExpand}` : 'border-transparent bg-neutral-50'}`}
    >
      <AccordionTrigger
        className="p-0 hover:no-underline"
        showIcon={false}
        onClick={(e) => {
          if (isPreventExpand) {
            e.preventDefault()
          }
        }}
      >
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-3">
            {icon ? (
              <div className="relative h-[32px] w-[32px]">
                <Image
                  src={icon?.url || icon?.thumbnailURL || ''}
                  alt={'icon'}
                  fill
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <HealthcareIcon width={32} height={32} />
            )}

            <div
              className={`${isExpanded ? textColorExpand : ''} flex gap-2 ${directionName === 'flex-col' ? 'flex-col items-start' : 'flex-row items-center'} justify-start`}
            >
              <div className="typo-body-6 truncate">{localizedName?.[primaryLanguage]}</div>
              <div
                className={`typo-body-7 truncate ${isExpanded ? textColorExpand : 'text-subdued'}`}
              >
                {directionName === 'flex-row'
                  ? `(${localizedName?.[secondaryLanguage]})`
                  : localizedName?.[secondaryLanguage]}
              </div>
            </div>
          </div>
        </div>

        {isShowButtonExpand && (
          <Button
            variant={'blank'}
            className="flex h-fit items-center gap-2 p-0 text-primary"
            onClick={() => handleExpandBox(!isExpanded)}
          >
            <Image
              src={isExpanded ? arrowPrimaryIcon : arrowDown}
              alt="arrow"
              width={18}
              height={18}
              className={`${isExpanded ? 'rotate-[180deg]' : ''}`}
            />
          </Button>
        )}
      </AccordionTrigger>

      <AccordionContent className="flex h-[calc(320px-80px)] p-0">
        <div className="mt-3 flex flex-1 flex-col gap-3">
          <div className="flex flex-1 flex-col gap-3">
            {bodyPartsSlice?.map((part, index) => {
              const { name, id } = part as BodyPart
              const localizedBodyPartName = name as unknown as LocalizeField<string>
              return (
                localizedBodyPartName && (
                  <div className="typo-body-6" key={`${id}-${index}`}>
                    {localizedBodyPartName?.[primaryLanguage]}{' '}
                    {secondaryLanguage && localizedBodyPartName?.[secondaryLanguage] && (
                      <span className="text-ellipsis">{` (${localizedBodyPartName?.[secondaryLanguage]})`}</span>
                    )}
                  </div>
                )
              )
            })}

            <ul className="!list-disc space-y-2 px-5">
              {symptomsSlice?.map((symptom, index) => {
                const { name, id } = symptom as Symptom
                const localizedSymptomName = name as unknown as LocalizeField<string>
                return (
                  <li className="typo-body-7" key={`${id}-${index}`}>
                    {localizedSymptomName?.[primaryLanguage]}{' '}
                    {secondaryLanguage && localizedSymptomName?.[secondaryLanguage] && (
                      <span className="text-ellipsis">{` (${localizedSymptomName?.[secondaryLanguage]})`}</span>
                    )}
                  </li>
                )
              })}
            </ul>

            {(bodyPartsRemaining.length > 0 || symptomsRemaining.length > 0) && (
              <ShowMorePopup
                bodyParts={bodyPartsRemaining}
                symptoms={symptomsRemaining}
                primaryLanguage={primaryLanguage}
                secondaryLanguage={secondaryLanguage}
              />
            )}
          </div>

          <a target="_blank" href={googleMapUrl} className="flex items-center justify-end gap-2">
            <Button variant={'blank'} className="flex h-fit items-center gap-2 p-0 text-green-500">
              {t('MES-131')}

              <Image alt="map" src={mapIcon} width={18} height={18} />
            </Button>
          </a>

          <Link
            href={`/medical-handbook/faculties/${faculty.id}${query ? `?q=${query}` : ''}`}
            className="flex items-center justify-end gap-2"
          >
            <Button variant={'blank'} className="flex h-fit items-center gap-2 p-0 text-primary">
              {t('MES-226')}

              <Image
                src={arrowPrimaryIcon}
                alt="arrow"
                width={18}
                height={18}
                className="rotate-[270deg]"
              />
            </Button>
          </Link>
        </div>
      </AccordionContent>
    </AccordionItem>
  )
}

export default FacultiesBox
