'use client'
import { Accordion } from '@/components/ui/Accordion/Accordion'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useGetInfiniteFaculties } from '@/hooks/query/faculty/useGetInfiniteFaculties'
import { useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import FacultiesBox from '../FacultiesBox/FacultiesBox'

const FacultiesList: React.FC = () => {
  const { faculties, fetchNextPage, hasNextPage, isGetFacultiesLoading } = useGetInfiniteFaculties({
    params: {
      limit: 40,
      locale: 'all',
      select: {
        questions: false,
        createdAt: false,
        updatedAt: false,
      },
      // where: {
      //   'subscription.type': {
      //     equals: type,
      //   },
      // },
    },
  })

  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  const facultiesList = faculties?.pages.flatMap((page) => page?.docs)

  return (
    <div className="mt-3">
      {isGetFacultiesLoading && !faculties ? (
        <div className="grid grid-cols-3 gap-3">
          {new Array(12).fill(0).map((_, i) => {
            return <Skeleton key={i} className="h-[52px] w-full"></Skeleton>
          })}
        </div>
      ) : (
        <>
          {faculties && (
            <InfiniteScroll
              dataLength={faculties.pages.length}
              next={() => {
                if (hasNextPage) {
                  fetchNextPage()
                }
              }}
              hasMore={hasNextPage}
              loader={
                <div className="flex items-center justify-center">
                  <Spinner></Spinner>
                </div>
              }
              scrollThreshold={0.8}
              className="space-y-3 !overflow-hidden"
            >
              <Accordion
                onValueChange={(value) => setExpandedItems(new Set(value))}
                type="multiple"
                className="grid grid-cols-3 gap-3 xl:grid-cols-4"
                key={'faculties'}
              >
                {facultiesList?.map((faculty) => {
                  return (
                    <FacultiesBox
                      faculty={faculty}
                      key={faculty.id}
                      isExpanded={expandedItems.has(faculty.id)}
                    />
                  )
                })}
              </Accordion>
            </InfiniteScroll>
          )}
        </>
      )}
    </div>
  )
}

export default FacultiesList
