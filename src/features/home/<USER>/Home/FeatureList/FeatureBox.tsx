import { StaticImport } from 'next/dist/shared/lib/get-img-props'
import Image from 'next/image'

type FeatureBoxType = {
  text: string
  source: string | StaticImport
  alt: string
  height?: number
  width?: number
  bgColor?: string
}

const FeatureBox: React.FC<FeatureBoxType> = ({
  text,
  source,
  alt,
  height = 64,
  width = 64,
  bgColor,
}) => {
  return (
    <div
      className={`flex cursor-pointer items-center justify-start gap-3 rounded-lg px-1 px-[22px] py-4 ${bgColor} `}
    >
      <div className="">
        <Image src={source} alt={alt} height={height} width={width} />
      </div>

      <span className="typo-body-10 text-white">{text}</span>
    </div>
  )
}

export default FeatureBox
