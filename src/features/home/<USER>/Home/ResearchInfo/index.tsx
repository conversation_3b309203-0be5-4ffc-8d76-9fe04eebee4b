import SearchInfoIcon from '@/assets/icons/location.svg'
import { HomeSearchInputTrigger } from '@/features/home/<USER>/HomeSearch/HomeSearchInputTrigger'
import { SearchTips } from '@/features/home/<USER>/HomeSearch/SearchTips'
import { SearchTip } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

interface HomeSearchProps {
  searchTips: SearchTip[] | null
}

const ResearchInfo: React.FC<HomeSearchProps> = ({ searchTips }) => {
  const t = useTranslations()

  return (
    <>
      <div className="flex items-center gap-4 rounded-t-lg bg-[linear-gradient(305deg,#5BAEF3_-1.86%,#1764E0_75.91%)] px-[22px] py-[20px]">
        <div className="flex flex-1 flex-col gap-4">
          <div className="typo-heading-7 text-white">{t('MES-727')}</div>
          <HomeSearchInputTrigger></HomeSearchInputTrigger>
        </div>
        <Image src={SearchInfoIcon} alt={'search-icon'} height={110} width={110} />
      </div>
      {/* Tips */}
      {searchTips && <SearchTips tips={searchTips} classText="typo-body-3" />}
    </>
  )
}

export default ResearchInfo
