'use client'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useGetInfiniteProductsV2 } from '@/features/product-v2/hooks/query/useGetInfiniteProductsV2'
import { ProductsCard } from '@/features/search-summary/components/SearchResults/ProductsV2SearchResult'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { Product } from '@/payload-types'
import { useTranslations } from 'next-intl'
import { parse } from 'qs-esm'
import { useEffect } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import SearchPopover from '../SearchPopover/SearchPopover'

type SearchProductsProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
  isViewAll?: boolean
  className?: string
  onViewAll?: () => void
  onGetTotalCount: (totalCount: number) => void
  onLoadingData: (isLoading: boolean) => void
}

const renderProducts = (group: Product[]) => (
  <div className="mt-3 grid grid-cols-6 gap-3">
    {group.map((product, index) => {
      return (
        <ProductsCard
          key={`product-${product.id}-${index}`}
          item={product}
          isNative={false}
        ></ProductsCard>
      )
    })}
  </div>
)

const PRODUCTS_SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'categories.title', isLocalized: true },
  { path: 'keywords.name', isLocalized: true, type: 'equals' },
  { path: 'title', isLocalized: false },
  { path: 'description', isLocalized: false },
]

const SearchProducts: React.FC<SearchProductsProps> = ({
  isViewAll = false,
  paramsValue,
  className,
  onViewAll,
  onGetTotalCount,
  onLoadingData,
}) => {
  const t = useTranslations()

  const { q: query } = parse(paramsValue as unknown as string)

  const { queryFilters } = useGenerateSearchQueryFilter({
    query: query,
    searchAbleFields: PRODUCTS_SEARCHABLE_FIELDS,
  })

  const { productsV2, isGetProductsV2Loading, fetchNextPage, hasNextPage } =
    useGetInfiniteProductsV2({
      params: {
        limit: 30,
        locale: 'all',
        where: {
          and: [
            {
              or: queryFilters,
            },
          ],
        },
      },
      config: {
        enabled: true,
      },
    })

  const flatItems = productsV2?.pages.flatMap((page) => page?.docs).filter((item) => !!item) || []
  const totalCount = productsV2?.pages[0]?.totalDocs || 0

  const displayItems = isViewAll ? flatItems : flatItems.slice(0, 6)

  useEffect(() => {
    onGetTotalCount(totalCount)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalCount])

  useEffect(() => {
    onLoadingData(isGetProductsV2Loading)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isGetProductsV2Loading])

  return (
    <div className={`mt-3 ${className}`}>
      <div className="typo-body-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {t('MES-567')} ({totalCount})
          <SearchPopover tooltipContent={t('MES-570')} />
        </div>
        {!isViewAll && onViewAll && totalCount > 0 && (
          <button
            onClick={onViewAll}
            className="typo-body-6 cursor-pointer text-primary-500 hover:text-primary-600"
          >
            {t('MES-476') || 'Xem tất cả'}
          </button>
        )}
      </div>

      {isGetProductsV2Loading && flatItems.length === 0 ? (
        <div className="mt-3 grid grid-cols-6 gap-3">
          {Array.from({ length: 6 }, (_, index) => (
            <div
              key={index}
              className="flex min-h-[200px] w-full flex-col gap-2 rounded-lg bg-neutral-50 px-3 py-2"
            >
              <Skeleton className="w-full flex-1"></Skeleton>
              <Skeleton className="h-10 w-full"></Skeleton>
            </div>
          ))}
        </div>
      ) : isViewAll ? (
        <InfiniteScroll
          dataLength={flatItems.length}
          next={() => {
            if (hasNextPage && !isGetProductsV2Loading) {
              fetchNextPage?.()
            }
          }}
          hasMore={!!hasNextPage}
          loader={
            <div className="flex justify-center py-4">
              <Spinner size={8} />
            </div>
          }
          scrollThreshold={0.8}
        >
          {renderProducts(displayItems)}
        </InfiniteScroll>
      ) : (
        renderProducts(flatItems)
      )}
    </div>
  )
}

export default SearchProducts
