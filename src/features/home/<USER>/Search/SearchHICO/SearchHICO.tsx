'use client'

import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import {
  KeywordsExplanation,
  KeywordsExplanationAccess,
} from '@/features/search-summary/components/KeywordsExplanation/KeywordsExplanation'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetExplainKeywords } from '@/hooks/query/ai-bot/useGetExplainKeywords'
import { useCheckKeywordExist } from '@/hooks/query/keyword/useCheckKeywordExist'
import { processQueryTerms } from '@/utilities/processQueryTerms'
import { parse } from 'qs-esm'
import React, { useMemo } from 'react'

type SearchHiCoProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
}
const SearchHiCo: React.FC<SearchHiCoProps> = ({ paramsValue }) => {
  const { user } = useAuthentication()
  const { primaryLanguage } = useAppLanguage()

  const { q: query } = parse(paramsValue as unknown as string)

  const splitQuery = useMemo(() => {
    return Array.isArray(query) ? query.map((item) => String(item)) : query ? [String(query)] : []
  }, [query])

  const primaryLangProcessedQueryTerms = useMemo(
    () => processQueryTerms(splitQuery, false),
    //eslint-disable-next-line react-hooks/exhaustive-deps
    [JSON.stringify(splitQuery)],
  )

  // Check keyword exist
  const { checkKeywordExist, isCheckKeywordExistLoading } = useCheckKeywordExist({
    params: {
      keyword: splitQuery?.[0],
    },
    useQueryOptions: {
      enabled: Boolean(splitQuery?.[0]),
      staleTime: 5 * 60 * 1000,
    },
  })

  // KEYWORDS EXPLANATION
  const isEnableExplainKeywords = useMemo(
    () => Boolean(user && checkKeywordExist?.exist === false && !isCheckKeywordExistLoading),
    [checkKeywordExist?.exist, isCheckKeywordExistLoading, user],
  )

  const { explainKeywords, isGetExplainKeywordsLoading } = useGetExplainKeywords({
    params: {
      keywords: primaryLangProcessedQueryTerms,
      targetLanguage: primaryLanguage,
    },
    useQueryOptions: {
      enabled: isEnableExplainKeywords,
      staleTime: 60000,
    },
  })

  return (
    <div className="mt-3">
      {user ? (
        isGetExplainKeywordsLoading ? (
          <div className="flex h-48 flex-col gap-3 rounded-lg bg-primary-50 p-4">
            <Skeleton className="h-9 w-28"></Skeleton>
            <Skeleton className="h-6 w-36"></Skeleton>
            <Skeleton className="h-9 w-full"></Skeleton>
            <Skeleton className="h-full w-full"></Skeleton>
          </div>
        ) : (
          <>
            <KeywordsExplanation
              explanations={explainKeywords?.explainations ?? []}
              isLoading={isGetExplainKeywordsLoading}
            />
          </>
        )
      ) : (
        <>
          <KeywordsExplanationAccess />
        </>
      )}
    </div>
  )
}

export default SearchHiCo
