'use client'

import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'
import { TabType } from '../SearchWrapper/SearchWrapper'

const TABS = [
  {
    value: 'all' as TabType,
    label: 'MES-141',
    type: 'all',
  },
  {
    value: 'bot' as TabType,
    label: 'MES-639',
    type: 'bot',
  },
  {
    value: 'faculties' as TabType,
    label: 'MES-564',
    type: 'faculties',
  },
  {
    value: 'keywords' as TabType,
    label: 'MES-565',
    type: 'keywords',
  },
  {
    value: 'products' as TabType,
    label: 'MES-567',
    type: 'products',
  },
]

type SearchTabsProps = {
  activeTab: TabType
  totalSearchData: {
    keywords: number
    faculties: number
    products: number
  }
  onTabChange: (tab: TabType) => void
}

const SearchTabs: React.FC<SearchTabsProps> = ({ activeTab, totalSearchData, onTabChange }) => {
  const t = useTranslations()

  const [totalSearchCount, setTotalSearchCount] = useState<number>(0)

  useEffect(() => {
    if (activeTab !== 'all') {
      setTotalSearchCount(totalSearchData[activeTab])
      return
    }
    const totalCount = Object.values(totalSearchData).reduce((acc, count) => acc + count, 0)
    setTotalSearchCount(totalCount)
  }, [totalSearchData, activeTab])

  return (
    <>
      <div className="typo-heading-7 mt-3">
        {t('MES-71')} ({totalSearchCount ?? 0})
      </div>

      <div className="mt-4 flex items-center gap-2">
        {TABS.map((tab) => {
          const isDisabled =
            (tab.value !== 'all' && totalSearchData[tab.type] === 0) ||
            (tab.value !== 'all' && totalSearchCount && tab.type === 'bot')

          return (
            <div
              key={tab.value}
              onClick={() => {
                if (isDisabled) return
                onTabChange(tab.value)
              }}
              className={`typo-body-6 flex items-center gap-2 rounded-xl border px-3 py-[6px] transition ${
                activeTab === tab.value
                  ? 'border-primary-500 bg-white text-primary-500'
                  : 'border-transparent bg-neutral-100 text-subdued'
              } ${isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} `}
            >
              {t(tab.label)}{' '}
              {totalSearchData[tab.type] !== undefined ? (
                `(${totalSearchData[tab.type]})`
              ) : tab.type !== 'all' ? (
                <div className="h-[6px] w-[6px] rounded-full bg-custom-warning-500"></div>
              ) : (
                ''
              )}
            </div>
          )
        })}
      </div>
    </>
  )
}

export default SearchTabs
