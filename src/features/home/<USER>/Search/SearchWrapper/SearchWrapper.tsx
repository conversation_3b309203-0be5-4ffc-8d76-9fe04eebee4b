'use client'

import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useDebounce } from '@/utilities/useDebounce'
import { parse } from 'qs-esm'
import { useEffect, useMemo, useState } from 'react'
import SearchEmpty from '../SearchEmpty/SearchEmpty'
import SearchFaculties from '../SearchFaculties/SearchFaculties'
import SearchHeader from '../SearchHeader/SearchHeader'
import SearchHiCo from '../SearchHICO/SearchHICO'
import SearchKeywords from '../SearchKeywords/SearchKeywords'
import { SearchPopulated } from '../SearchPopulated/SearchPopulated'
import SearchProducts from '../SearchProducts/SearchProducts'
import SearchTabs from '../SearchTabs/SearchTabs'

type SearchWrapperProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
}

export type TabType = 'all' | 'bot' | 'faculties' | 'keywords' | 'products'

const SearchWrapper: React.FC<SearchWrapperProps> = ({ paramsValue }) => {
  const { updateSearchQuery } = useSearchQuery()

  // Parse URL parameters
  const { q: query, tab } = parse(paramsValue as unknown as string)

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')

  const searchDebounce = useDebounce(searchQuery, 500)

  const [totalSearchData, setTotalSearchData] = useState<{
    keywords: number
    faculties: number
    products: number
  }>({
    keywords: 0,
    faculties: 0,
    products: 0,
  })

  const [isLoadingData, setIsLoadingData] = useState<{
    keywords: boolean
    faculties: boolean
    products: boolean
  }>({
    keywords: true,
    faculties: true,
    products: true,
  })

  const isEmptySearchResult = useMemo(() => {
    return (
      totalSearchData.keywords === 0 &&
      totalSearchData.faculties === 0 &&
      totalSearchData.products === 0 &&
      !isLoadingData.keywords &&
      !isLoadingData.faculties &&
      !isLoadingData.products
    )
  }, [totalSearchData, isLoadingData])

  useEffect(() => {
    updateSearchQuery({ q: searchDebounce || undefined }, 'replace')
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchDebounce])

  // Initialize state from URL parameters
  useEffect(() => {
    if (tab && typeof tab === 'string') {
      setActiveTab(tab as TabType)
    }
    if (query && typeof query === 'string') {
      setSearchQuery(query)
    }
  }, [tab, query])

  // Handle tab change
  const handleTabChange = (newTab: TabType) => {
    setActiveTab(newTab)
    updateSearchQuery({ tab: newTab }, 'replace')
  }

  // Handle search query change
  const handleSearchQueryChange = (newQuery: string) => {
    setSearchQuery(newQuery)
  }

  // Handle keyword click from SearchPopulated
  const handleKeywordClick = (keyword: string) => {
    setSearchQuery(keyword)
    updateSearchQuery({ q: keyword }, 'replace')
  }

  return (
    <div className="h-full bg-custom-background-hover px-16 py-6">
      <div className="rounded-3xl bg-white p-4">
        <SearchHeader
          paramsValue={paramsValue}
          onSearchChange={handleSearchQueryChange}
          searchQuery={searchQuery}
        />

        <SearchTabs
          activeTab={activeTab}
          totalSearchData={totalSearchData}
          onTabChange={handleTabChange}
        />

        {/* Show all components when tab is 'all' */}

        <>
          <SearchEmpty
            className={isEmptySearchResult && activeTab !== 'bot' ? 'block' : 'hidden'}
          />

          <div className={isEmptySearchResult ? 'hidden' : 'block'}>
            <SearchKeywords
              className={activeTab === 'all' || activeTab === 'keywords' ? 'block' : 'hidden'}
              paramsValue={paramsValue}
              isViewAll={false}
              onViewAll={() => handleTabChange('keywords')}
              onGetTotalCount={(totalCount) =>
                setTotalSearchData((prev) => ({ ...prev, keywords: totalCount }))
              }
              onLoadingData={(isLoading) =>
                setIsLoadingData((prev) => ({ ...prev, keywords: isLoading }))
              }
            />
            <SearchFaculties
              className={activeTab === 'all' || activeTab === 'faculties' ? 'block' : 'hidden'}
              paramsValue={paramsValue}
              isViewAll={false}
              onViewAll={() => handleTabChange('faculties')}
              onGetTotalCount={(totalCount) =>
                setTotalSearchData((prev) => ({ ...prev, faculties: totalCount }))
              }
              onLoadingData={(isLoading) =>
                setIsLoadingData((prev) => ({ ...prev, faculties: isLoading }))
              }
            />
            <SearchProducts
              className={activeTab === 'all' || activeTab === 'products' ? 'block' : 'hidden'}
              paramsValue={paramsValue}
              isViewAll={false}
              onViewAll={() => handleTabChange('products')}
              onGetTotalCount={(totalCount) =>
                setTotalSearchData((prev) => ({ ...prev, products: totalCount }))
              }
              onLoadingData={(isLoading) =>
                setIsLoadingData((prev) => ({ ...prev, products: isLoading }))
              }
            />
          </div>
        </>

        {/* Show specific component when tab is selected */}
        {activeTab === 'bot' && <SearchHiCo paramsValue={paramsValue} />}

        {/* SearchPopulated always shows */}
        <SearchPopulated onKeywordClick={handleKeywordClick} />
      </div>
    </div>
  )
}

export default SearchWrapper
