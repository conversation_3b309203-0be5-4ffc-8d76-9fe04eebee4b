import {
  Popover,
  <PERSON>over<PERSON>rrow,
  <PERSON>overContent,
  PopoverTrigger,
} from '@/components/ui/Popover/Popover'
import Image from 'next/image'

import NoteIcon from '@/assets/icons/message-question.svg'

type SearchPopoverProps = {
  tooltipContent: string
}
const SearchPopover: React.FC<SearchPopoverProps> = ({ tooltipContent }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="shrink-0 cursor-pointer">
          <Image src={NoteIcon} alt="question" width={18} height={18} />
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="center"
        sideOffset={4}
        className="typo-body-7 relative rounded-[6px] border-none bg-custom-informative-600 p-3 text-white"
      >
        {tooltipContent}
        <PopoverArrow asChild>
          <svg
            className="rotate-180"
            width={16}
            height={8}
            viewBox="0 0 16 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M6.58579 1.41421C7.36684 0.633163 8.63316 0.633165 9.41421 1.41421L16 8L-3.49691e-07 8L6.58579 1.41421Z"
              fill="#3B82F6"
            />
          </svg>
        </PopoverArrow>
      </PopoverContent>
    </Popover>
  )
}

export default SearchPopover
