'use client'
import { useGetInfiniteKeywords } from '@/hooks/query/keyword/useGetInfiniteKeywords'
import { LocalizeField } from '@/types/custom-payload.type'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React, { useEffect } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import SearchPopover from '../SearchPopover/SearchPopover'

import archiveIcon from '@/assets/icons/archive-add.svg'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { LocaleEnum } from '@/enums/locale.enum'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { parse } from 'qs-esm'

type SearchKeywordsProps = {
  isViewAll?: boolean
  className?: string
  onViewAll?: () => void
  onGetTotalCount: (totalCount: number) => void
  onLoadingData: (isLoading: boolean) => void
  paramsValue: {
    [key: string]: string | undefined
  }
}

const SEARCHABLE_FIELDS: FilterField[] = [{ path: 'name', type: 'like', isLocalized: true }]

const SearchKeywords: React.FC<SearchKeywordsProps> = ({
  isViewAll = false,
  paramsValue,
  className,
  onViewAll,
  onGetTotalCount,
  onLoadingData,
}) => {
  const t = useTranslations()

  const { q: query } = parse(paramsValue as unknown as string)

  const { queryFilters } = useGenerateSearchQueryFilter({
    query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  const { keywords, fetchNextPage, hasNextPage, isGetKeywordsLoading } = useGetInfiniteKeywords({
    params: {
      limit: isViewAll ? 30 : 6,
      locale: 'all',
      where: {
        and: [
          {
            or: queryFilters,
          },
        ],
      },
    },
    config: {
      enabled: true,
    },
  })

  const flatItems = keywords?.pages.flatMap((page) => page?.docs).filter((item) => !!item) || []
  const totalCount = keywords?.pages[0]?.totalDocs || 0

  const displayItems = isViewAll ? flatItems : flatItems

  const renderKeywordGrid = () => (
    <div className="mt-3 grid grid-cols-3 gap-3">
      {displayItems.map((item, index) => {
        const localizedName = item?.name as unknown as LocalizeField<string>
        return (
          <div
            key={`${item.id}-${index}`}
            className="flex min-w-[200px] flex-1 shrink-0 snap-start flex-col gap-2 rounded-lg bg-primary-50 p-4"
          >
            <div className="flex items-center justify-between gap-x-2">
              <div className="flex items-center gap-3">
                <p className="typo-body-2 text-primary-500"> {localizedName?.[LocaleEnum.JA]}</p>
                {item?.hiragana && <p className="typo-body-7 text-subdued">/{item?.hiragana}/</p>}
              </div>
              <div className="size-5 shrink-0 cursor-pointer self-start">
                <Image src={archiveIcon} alt="audio" width={20} height={20} className="size-5" />
              </div>
            </div>
            <p className="typo-body-7">{localizedName?.[LocaleEnum.VI]}</p>
          </div>
        )
      })}
    </div>
  )

  useEffect(() => {
    onGetTotalCount(totalCount)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalCount])

  useEffect(() => {
    onLoadingData(isGetKeywordsLoading)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isGetKeywordsLoading])

  return (
    <div className={`mt-3 ${className}`}>
      <div className="typo-body-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {t('MES-565')} ({totalCount})
          <SearchPopover tooltipContent={t('MES-568')} />
        </div>
        {!isViewAll && onViewAll && totalCount > 0 && (
          <button
            onClick={onViewAll}
            className="typo-body-6 cursor-pointer text-primary-500 hover:text-primary-600"
          >
            {t('MES-476') || 'Xem tất cả'}
          </button>
        )}
      </div>

      {isGetKeywordsLoading && displayItems.length === 0 ? (
        <div className="mt-3 grid grid-cols-3 gap-3">
          {Array.from({ length: 6 }, (_, index) => (
            <Skeleton key={index} className="min-h-[90px] w-full"></Skeleton>
          ))}
        </div>
      ) : isViewAll ? (
        <InfiniteScroll
          dataLength={displayItems.length}
          next={() => {
            if (hasNextPage && !isGetKeywordsLoading) {
              fetchNextPage?.()
            }
          }}
          hasMore={!!hasNextPage}
          loader={
            <div className="flex justify-center py-4">
              <Spinner size={8} />
            </div>
          }
          scrollThreshold={0.8}
        >
          {renderKeywordGrid()}
        </InfiniteScroll>
      ) : (
        renderKeywordGrid()
      )}
    </div>
  )
}

export default SearchKeywords
