import ReCaptcha from '@/components/ReCaptcha/ReCaptcha'
import { LOGIN_SESSION_COOKIE_KEY, PORTAL_TOKEN_KEY } from '@/constants/global.constant'
import { HomeDidYouKnow } from '@/features/home/<USER>/HomeDidYouKnow/HomeDidYouKnow'
import { HomeSubscribe } from '@/features/home/<USER>/HomeSubscribe/HomeSubscribe'
import {
  getBodyPartsCache,
  getHomeFacultiesCache,
  getNewestVersionCache,
  getRandomFactsCache,
  getRandomKeywordsCache,
  getRandomSearchTipsCache,
  getSubscriptionPlansCache,
} from '@/features/home/<USER>/Home/HomeContainer'
import FeatureList from '@/features/home/<USER>/Home/FeatureList'
import HomeCard from '@/features/home/<USER>/Home/HomeCard'
import HomeContact from '@/features/home/<USER>/Home/HomeContact/HomeContact'
import { HomeDailyVocabulary } from '@/features/home/<USER>/Home/HomeDailyVocabulary/HomeDailyVocabulary'
import { HomeFaculties } from '@/features/home/<USER>/Home/HomeFaculties/HomeFaculties'
import { HomeMedicines } from '@/features/home/<USER>/Home/HomeMedicines/HomeMedicines'
import { HomePosts } from '@/features/home/<USER>/Home/HomePosts/HomePosts'
import HomeSearchHistory from '@/features/home/<USER>/Home/HomeSearchHistory/HomeSearchHistory'
import ResearchInfo from '@/features/home/<USER>/Home/ResearchInfo'
import { keywordService } from '@/services/keyword.service'
import { getLocale } from 'next-intl/server'
import { cookies } from 'next/headers'
import { cache } from 'react'

const MainLayout: React.FC = async () => {
  const locale = await getLocale()

  const [
    _,
    faculties,
    bodyParts,
    __,
    randomKeywords,
    randomFacts,
    randomSearchTips,
    keywordSearchHistory,
  ] = await Promise.all([
    getNewestVersionCache(),
    getHomeFacultiesCache(locale),
    getBodyPartsCache(locale),
    getSubscriptionPlansCache(),
    getRandomKeywordsCache(),
    getRandomFactsCache(locale),
    getRandomSearchTipsCache(locale),
    getKeywordSearchHistory(locale),
  ])

  return (
    <div className="flex max-w-full gap-6 overflow-hidden bg-custom-background-hover px-16 py-6">
      <div className="inline-flex min-w-0 flex-[7] flex-col gap-3 rounded-3xl bg-white p-4">
        <ResearchInfo searchTips={randomSearchTips?.docs ?? []} />

        <FeatureList />

        <HomeFaculties faculties={faculties?.docs ?? []} />

        <HomeMedicines bodyParts={bodyParts ?? null}></HomeMedicines>

        <HomePosts />

        <HomeCard />
      </div>

      <div className="inline-flex h-fit min-w-0 flex-[3] flex-col gap-3 rounded-3xl bg-white p-4">
        {/* <SearchMedicineByImage /> */}

        <HomeContact />

        <HomeSearchHistory keywords={keywordSearchHistory?.docs ?? []} />

        <HomeDidYouKnow classStyleSection="px-0" fact={randomFacts?.docs?.[0] ?? null} />

        <HomeDailyVocabulary keywords={randomKeywords?.docs ?? []}></HomeDailyVocabulary>

        <ReCaptcha>
          <HomeSubscribe classStyleSection={'p-0 pb-2'} />
        </ReCaptcha>
      </div>
    </div>
  )
}

export default MainLayout

export const getKeywordSearchHistory = cache(async (locale: string) => {
  const cookieStore = await cookies()
  const token = cookieStore.get(PORTAL_TOKEN_KEY)?.value
  const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value

  try {
    const randomSearchTips = await keywordService.getKeywordSearchHistory({
      params: {
        limit: 5,
        locale: locale,
      },
      options: {
        next: {
          revalidate: 60 * 60 * 12,
        },
        cache: 'default',

        headers: {
          Cookie: `${PORTAL_TOKEN_KEY}=${token}; ${LOGIN_SESSION_COOKIE_KEY}=${sessionToken}`,
        },
      },
    })
    return randomSearchTips
  } catch (error) {
    console.log(error)
    return null
  }
})
