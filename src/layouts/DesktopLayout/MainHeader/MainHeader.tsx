import logoHico from '@/assets/icons/logo-hico.svg'
import Image from 'next/image'
import { LanguageDropdown } from './LanguageDropdown'

const MainHeader: React.FC = () => {
  return (
    <div className="flex justify-between gap-2 bg-white px-[64px] py-[26px]">
      <div className="typo-heading-7 flex items-center gap-3">
        <Image src={logoHico} alt="logo-Hico" height={36} width={36} />
        <span>HICO</span>
      </div>

      <div className="flex items-center gap-3">
        {/* <MainAuthentication /> */}
        <LanguageDropdown />
      </div>
    </div>
  )
}

export default MainHeader
